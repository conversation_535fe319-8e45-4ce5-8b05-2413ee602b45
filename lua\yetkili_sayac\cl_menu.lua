-- <PERSON><PERSON><PERSON> - İstemci Menü

YetkiliSayac.Menu = YetkiliSayac.Menu or {}

-- Ana menü paneli
function YetkiliSayac.Menu.Open()
    if IsValid(YetkiliSayac.Menu.Frame) then
        YetkiliSayac.Menu.Frame:Remove()
    end
    
    -- Sunucudan veri iste
    net.Start("YetkiliSayac_RequestData")
    net.SendToServer()
    
    -- Ana çerçeve
    local frame = vgui.Create("DFrame")
    frame:SetSize(800, 600)
    frame:Center()
    frame:SetTitle("🚔 Yetkili Sayaç Sistemi")
    frame:SetVisible(true)
    frame:SetDraggable(true)
    frame:ShowCloseButton(true)
    frame:MakePopup()
    
    YetkiliSayac.Menu.Frame = frame
    
    -- Arka plan rengi
    frame.Paint = function(self, w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(45, 45, 45, 240))
        draw.RoundedBox(8, 2, 2, w-4, h-4, Color(35, 35, 35, 200))
    end
    
    -- Başlık
    local titleLabel = vgui.Create("DLabel", frame)
    titleLabel:SetPos(20, 30)
    titleLabel:SetSize(760, 30)
    titleLabel:SetText("Yetkili Meslek Süre İstatistikleri")
    titleLabel:SetFont("DermaLarge")
    titleLabel:SetTextColor(Color(255, 255, 255))
    
    -- Tab sistemi
    local propertySheet = vgui.Create("DPropertySheet", frame)
    propertySheet:SetPos(10, 70)
    propertySheet:SetSize(780, 520)
    
    -- Geçmiş sekmesi
    local historyPanel = vgui.Create("DPanel")
    historyPanel.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(50, 50, 50, 100))
    end
    
    YetkiliSayac.Menu.HistoryList = vgui.Create("DListView", historyPanel)
    YetkiliSayac.Menu.HistoryList:SetPos(10, 10)
    YetkiliSayac.Menu.HistoryList:SetSize(750, 470)
    YetkiliSayac.Menu.HistoryList:SetMultiSelect(false)
    YetkiliSayac.Menu.HistoryList:AddColumn("Meslek")
    YetkiliSayac.Menu.HistoryList:AddColumn("Başlangıç")
    YetkiliSayac.Menu.HistoryList:AddColumn("Bitiş")
    YetkiliSayac.Menu.HistoryList:AddColumn("Süre")
    
    propertySheet:AddSheet("📋 Geçmiş", historyPanel, "icon16/time.png")
    
    -- İstatistikler sekmesi
    local statsPanel = vgui.Create("DPanel")
    statsPanel.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(50, 50, 50, 100))
    end
    
    YetkiliSayac.Menu.StatsPanel = statsPanel
    
    propertySheet:AddSheet("📊 İstatistikler", statsPanel, "icon16/chart_bar.png")
    
    -- Aktif oturum sekmesi
    local activePanel = vgui.Create("DPanel")
    activePanel.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(50, 50, 50, 100))
    end
    
    YetkiliSayac.Menu.ActivePanel = activePanel
    
    propertySheet:AddSheet("⏱️ Aktif Oturum", activePanel, "icon16/clock.png")
    
    -- Yenile butonu
    local refreshBtn = vgui.Create("DButton", frame)
    refreshBtn:SetPos(680, 35)
    refreshBtn:SetSize(100, 25)
    refreshBtn:SetText("🔄 Yenile")
    refreshBtn.DoClick = function()
        net.Start("YetkiliSayac_RequestData")
        net.SendToServer()
    end
end

-- İstatistikler panelini oluştur
function YetkiliSayac.Menu.CreateStatsPanel(data)
    if not IsValid(YetkiliSayac.Menu.StatsPanel) then return end
    
    -- Önceki içeriği temizle
    YetkiliSayac.Menu.StatsPanel:Clear()
    
    local y = 20
    
    -- Başlık
    local titleLabel = vgui.Create("DLabel", YetkiliSayac.Menu.StatsPanel)
    titleLabel:SetPos(20, y)
    titleLabel:SetSize(400, 25)
    titleLabel:SetText("Meslek Bazında Toplam Süreler")
    titleLabel:SetFont("DermaDefaultBold")
    titleLabel:SetTextColor(Color(255, 255, 255))
    
    y = y + 40
    
    -- Her meslek için toplam süre
    for job, totalTime in pairs(data.totalTimes or {}) do
        if totalTime > 0 then
            local jobLabel = vgui.Create("DLabel", YetkiliSayac.Menu.StatsPanel)
            jobLabel:SetPos(30, y)
            jobLabel:SetSize(200, 20)
            jobLabel:SetText("🔹 " .. job .. ":")
            jobLabel:SetTextColor(Color(200, 200, 200))
            
            local timeLabel = vgui.Create("DLabel", YetkiliSayac.Menu.StatsPanel)
            timeLabel:SetPos(250, y)
            timeLabel:SetSize(300, 20)
            timeLabel:SetText(YetkiliSayac.FormatTime(totalTime))
            timeLabel:SetTextColor(Color(100, 255, 100))
            timeLabel:SetFont("DermaDefaultBold")
            
            y = y + 25
        end
    end
    
    -- Toplam süre hesapla
    local grandTotal = 0
    for _, time in pairs(data.totalTimes or {}) do
        grandTotal = grandTotal + time
    end
    
    if grandTotal > 0 then
        y = y + 20
        
        local totalLabel = vgui.Create("DLabel", YetkiliSayac.Menu.StatsPanel)
        totalLabel:SetPos(30, y)
        totalLabel:SetSize(200, 25)
        totalLabel:SetText("🏆 TOPLAM SÜRENİZ:")
        totalLabel:SetFont("DermaDefaultBold")
        totalLabel:SetTextColor(Color(255, 255, 100))
        
        local grandTotalLabel = vgui.Create("DLabel", YetkiliSayac.Menu.StatsPanel)
        grandTotalLabel:SetPos(250, y)
        grandTotalLabel:SetSize(300, 25)
        grandTotalLabel:SetText(YetkiliSayac.FormatTime(grandTotal))
        grandTotalLabel:SetTextColor(Color(255, 100, 100))
        grandTotalLabel:SetFont("DermaLarge")
    end
end

-- Aktif oturum panelini oluştur
function YetkiliSayac.Menu.CreateActivePanel(data)
    if not IsValid(YetkiliSayac.Menu.ActivePanel) then return end
    
    -- Önceki içeriği temizle
    YetkiliSayac.Menu.ActivePanel:Clear()
    
    if data.currentSession then
        local session = data.currentSession
        local currentDuration = session.currentTime - session.startTime
        
        -- Başlık
        local titleLabel = vgui.Create("DLabel", YetkiliSayac.Menu.ActivePanel)
        titleLabel:SetPos(20, 20)
        titleLabel:SetSize(400, 30)
        titleLabel:SetText("🟢 Aktif Oturum Bulundu!")
        titleLabel:SetFont("DermaLarge")
        titleLabel:SetTextColor(Color(100, 255, 100))
        
        -- Meslek
        local jobLabel = vgui.Create("DLabel", YetkiliSayac.Menu.ActivePanel)
        jobLabel:SetPos(30, 70)
        jobLabel:SetSize(150, 25)
        jobLabel:SetText("💼 Meslek:")
        jobLabel:SetFont("DermaDefaultBold")
        jobLabel:SetTextColor(Color(255, 255, 255))
        
        local jobValueLabel = vgui.Create("DLabel", YetkiliSayac.Menu.ActivePanel)
        jobValueLabel:SetPos(200, 70)
        jobValueLabel:SetSize(300, 25)
        jobValueLabel:SetText(session.job)
        jobValueLabel:SetTextColor(Color(100, 200, 255))
        jobValueLabel:SetFont("DermaDefaultBold")
        
        -- Başlangıç zamanı
        local startLabel = vgui.Create("DLabel", YetkiliSayac.Menu.ActivePanel)
        startLabel:SetPos(30, 100)
        startLabel:SetSize(150, 25)
        startLabel:SetText("🕐 Başlangıç:")
        startLabel:SetFont("DermaDefaultBold")
        startLabel:SetTextColor(Color(255, 255, 255))
        
        local startValueLabel = vgui.Create("DLabel", YetkiliSayac.Menu.ActivePanel)
        startValueLabel:SetPos(200, 100)
        startValueLabel:SetSize(300, 25)
        startValueLabel:SetText(os.date("%d/%m/%Y %H:%M:%S", session.startTime))
        startValueLabel:SetTextColor(Color(200, 200, 200))
        
        -- Mevcut süre
        local durationLabel = vgui.Create("DLabel", YetkiliSayac.Menu.ActivePanel)
        durationLabel:SetPos(30, 130)
        durationLabel:SetSize(150, 25)
        durationLabel:SetText("⏱️ Mevcut Süre:")
        durationLabel:SetFont("DermaDefaultBold")
        durationLabel:SetTextColor(Color(255, 255, 255))
        
        YetkiliSayac.Menu.DurationLabel = vgui.Create("DLabel", YetkiliSayac.Menu.ActivePanel)
        YetkiliSayac.Menu.DurationLabel:SetPos(200, 130)
        YetkiliSayac.Menu.DurationLabel:SetSize(300, 25)
        YetkiliSayac.Menu.DurationLabel:SetText(YetkiliSayac.FormatTime(currentDuration))
        YetkiliSayac.Menu.DurationLabel:SetTextColor(Color(255, 255, 100))
        YetkiliSayac.Menu.DurationLabel:SetFont("DermaDefaultBold")
        
        -- Süreyi güncelleyen timer
        YetkiliSayac.Menu.UpdateTimer = timer.Create("YetkiliSayac_UpdateDuration", 1, 0, function()
            if IsValid(YetkiliSayac.Menu.DurationLabel) then
                local newDuration = os.time() - session.startTime
                YetkiliSayac.Menu.DurationLabel:SetText(YetkiliSayac.FormatTime(newDuration))
            else
                timer.Remove("YetkiliSayac_UpdateDuration")
            end
        end)
        
    else
        -- Aktif oturum yok
        local noSessionLabel = vgui.Create("DLabel", YetkiliSayac.Menu.ActivePanel)
        noSessionLabel:SetPos(20, 20)
        noSessionLabel:SetSize(400, 30)
        noSessionLabel:SetText("🔴 Şu anda aktif yetkili oturumunuz bulunmuyor.")
        noSessionLabel:SetFont("DermaLarge")
        noSessionLabel:SetTextColor(Color(255, 100, 100))
        
        local infoLabel = vgui.Create("DLabel", YetkiliSayac.Menu.ActivePanel)
        infoLabel:SetPos(30, 70)
        infoLabel:SetSize(500, 60)
        infoLabel:SetText("Yetkili bir mesleğe geçtiğinizde süreniz otomatik olarak sayılmaya başlayacaktır.\n\nYetkili meslekler: " .. table.concat(YetkiliSayac.Config.YetkiliMeslekler, ", "))
        infoLabel:SetTextColor(Color(200, 200, 200))
        infoLabel:SetAutoStretchVertical(true)
        infoLabel:SetWrap(true)
    end
end

-- Sunucudan veri geldiğinde
net.Receive("YetkiliSayac_SendData", function()
    local data = net.ReadTable()
    
    if not IsValid(YetkiliSayac.Menu.Frame) then return end
    
    -- Geçmiş listesini doldur
    if IsValid(YetkiliSayac.Menu.HistoryList) then
        YetkiliSayac.Menu.HistoryList:Clear()
        
        for _, record in pairs(data.history or {}) do
            local startTime = os.date("%d/%m/%Y %H:%M", record.start_time)
            local endTime = record.end_time and os.date("%d/%m/%Y %H:%M", record.end_time) or "Devam ediyor"
            local duration = YetkiliSayac.FormatTime(record.duration)
            
            YetkiliSayac.Menu.HistoryList:AddLine(record.job_name, startTime, endTime, duration)
        end
    end
    
    -- İstatistikler panelini oluştur
    YetkiliSayac.Menu.CreateStatsPanel(data)
    
    -- Aktif oturum panelini oluştur
    YetkiliSayac.Menu.CreateActivePanel(data)
end)

-- Menü açma network mesajı
net.Receive("YetkiliSayac_OpenMenu", function()
    YetkiliSayac.Menu.Open()
end)

-- Menü kapandığında timer'ı temizle
hook.Add("OnGamemodeLoaded", "YetkiliSayac_CleanupTimer", function()
    if timer.Exists("YetkiliSayac_UpdateDuration") then
        timer.Remove("YetkiliSayac_UpdateDuration")
    end
end)
