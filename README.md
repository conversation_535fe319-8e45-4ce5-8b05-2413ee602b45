# Yetkili <PERSON>'s Mod DarkRP sunucuları için geliştirilmiş yetkili meslek süre takip sistemi.

## Özellikler

- ✅ Yetkili mesleklerde geçirilen süreyi otomatik takip
- ✅ Discord webhook entegrasyonu
- ✅ Detaylı istatistik menüsü
- ✅ Geçmiş kayıtları görüntüleme
- ✅ Aktif oturum takibi
- ✅ SQLite veritabanı desteği
- ✅ Çoklu meslek desteği

## Kurulum

1. Bu addon'u `garrysmod/addons/` klasörüne yerleştirin
2. Sunucuyu yeniden başlatın
3. Discord webhook URL'ini ayarlayın (isteğe bağlı)

## Konfigürasyon

### Yetkili Meslekleri Ayarlama

`lua/autorun/shared/sh_yetkili_sayac.lua` dosyasında `YetkiliMeslekler` listesini düzenleyin:

```lua
YetkiliMeslekler = {
    "police",
    "sheriff", 
    "swat",
    "mayor",
    "admin",
    "moderator"
}
```

### Discord Webhook Ayarlama

`lua/autorun/shared/sh_yetkili_sayac.lua` dosyasında `WebhookURL` değerini düzenleyin:

```lua
WebhookURL = "https://discord.com/api/webhooks/YOUR_WEBHOOK_ID/YOUR_WEBHOOK_TOKEN",
```

Discord'da webhook oluşturmak için:
1. Discord sunucunuzda bir kanala gidin
2. Kanal ayarları > Entegrasyonlar > Webhook'lar
3. "Yeni Webhook" oluşturun
4. Webhook URL'ini kopyalayın ve config dosyasına yapıştırın

## Kullanım

### Menü Açma

- Chat'te `!yetkilisayac` yazın
- Konsola `yetkili_sayac_menu` yazın
- F4 menüsünden "Yetkili Sayaç" sekmesini kullanın (DarkRP)

### Admin Komutları

- `yetkili_sayac_stats` - Tüm oyuncuların istatistiklerini görüntüle

## Menü Özellikleri

### 📋 Geçmiş Sekmesi
- Son 20 oturumunuzun detayları
- Başlangıç ve bitiş zamanları
- Her oturumdaki süre bilgisi

### 📊 İstatistikler Sekmesi
- Meslek bazında toplam süreler
- Genel toplam süre
- Renkli ve düzenli görünüm

### ⏱️ Aktif Oturum Sekmesi
- Şu anki aktif oturumunuz
- Gerçek zamanlı süre sayacı
- Oturum başlangıç zamanı

## Discord Bildirimleri

Sistem aşağıdaki durumlarda Discord'a bildirim gönderir:

- Oyuncu yetkili meslekten çıktığında
- Minimum süre (varsayılan 60 saniye) aşıldığında
- Oyuncu sunucudan ayrıldığında (aktif oturum varsa)

### Bildirim İçeriği

- 👤 Oyuncu adı ve SteamID
- 💼 Meslek adı
- ⏱️ Bu oturumdaki süre
- 📊 Bu meslekteki toplam süre
- 🕐 Çıkış zamanı

## Veritabanı

Sistem SQLite kullanır ve aşağıdaki bilgileri saklar:

- Oyuncu bilgileri (SteamID, isim)
- Meslek adı
- Oturum başlangıç/bitiş zamanları
- Süre bilgileri
- Oluşturulma tarihi

## Teknik Detaylar

### Dosya Yapısı

```
yetkilisayac/
├── addon.txt
├── README.md
├── lua/
│   ├── autorun/
│   │   ├── shared/sh_yetkili_sayac.lua
│   │   ├── server/sv_yetkili_sayac.lua
│   │   └── client/cl_yetkili_sayac.lua
│   └── yetkili_sayac/
│       ├── sv_database.lua
│       ├── sv_webhook.lua
│       └── cl_menu.lua
```

### Hook'lar

- `OnPlayerChangedTeam` - DarkRP meslek değişikliği
- `PlayerDisconnected` - Oyuncu ayrılması
- `PlayerInitialSpawn` - Oyuncu katılması

### Network Strings

- `YetkiliSayac_OpenMenu` - Menü açma
- `YetkiliSayac_RequestData` - Veri isteme
- `YetkiliSayac_SendData` - Veri gönderme
- `YetkiliSayac_JobStarted` - Meslek başlangıcı
- `YetkiliSayac_JobEnded` - Meslek bitişi

## Sorun Giderme

### Sistem çalışmıyor
1. Konsol hatalarını kontrol edin
2. DarkRP yüklü olduğundan emin olun
3. Meslek adlarının doğru olduğunu kontrol edin

### Discord webhook çalışmıyor
1. Config dosyasında webhook URL'inin doğru olduğundan emin olun
2. Webhook URL'inin "YOUR_WEBHOOK" içermediğinden emin olun
3. Minimum süre ayarını kontrol edin
4. Sunucu konsolunda webhook hata mesajlarını kontrol edin

### Menü açılmıyor
1. `!yetkilisayac` komutunu doğru yazdığınızdan emin olun
2. Konsol komutunu deneyin: `yetkili_sayac_menu`
3. F4 menüsünü kontrol edin

## Destek

Herhangi bir sorun yaşarsanız:

1. Konsol loglarını kontrol edin
2. Addon dosyalarının doğru yerde olduğundan emin olun
3. Sunucuyu yeniden başlatmayı deneyin

## Lisans

Bu addon açık kaynak kodludur ve özgürce kullanılabilir.
