-- Yet<PERSON><PERSON>mi - İstemci Kodu

-- <PERSON><PERSON> m<PERSON> yükle
include("yetkili_sayac/cl_menu.lua")

-- <PERSON><PERSON> komutu ile menü a<PERSON>ma (istemci tarafı)
hook.Add("OnPlayerChat", "YetkiliSayac_ClientChatCommand", function(ply, strText, bTeamOnly, bPlayerIsDead)
    if ply == LocalPlayer() and string.lower(strText) == string.lower(YetkiliSayac.Config.MenuCommand) then
        YetkiliSayac.Menu.Open()
    end
end)

-- F4 menüsüne buton ekleme (DarkRP uyumluluğu)
hook.Add("OnGamemodeLoaded", "YetkiliSayac_AddF4Button", function()
    if DarkRP and DarkRP.addF4MenuTab then
        DarkRP.addF4MenuTab("Yetkili Sayaç", function(parent)
            local panel = vgui.Create("DPanel", parent)
            panel:Dock(FILL)
            panel.Paint = function(self, w, h)
                draw.RoundedBox(8, 0, 0, w, h, Color(45, 45, 45, 200))
            end
            
            local titleLabel = vgui.Create("DLabel", panel)
            titleLabel:SetPos(20, 20)
            titleLabel:SetSize(400, 30)
            titleLabel:SetText("🚔 Yetkili Sayaç Sistemi")
            titleLabel:SetFont("DermaLarge")
            titleLabel:SetTextColor(Color(255, 255, 255))
            
            local infoLabel = vgui.Create("DLabel", panel)
            infoLabel:SetPos(20, 60)
            infoLabel:SetSize(500, 60)
            infoLabel:SetText("Bu sistem yetkili mesleklerde geçirdiğiniz süreyi takip eder.\nDetaylı bilgi ve istatistikler için aşağıdaki butona tıklayın.")
            infoLabel:SetTextColor(Color(200, 200, 200))
            infoLabel:SetAutoStretchVertical(true)
            infoLabel:SetWrap(true)
            
            local openBtn = vgui.Create("DButton", panel)
            openBtn:SetPos(20, 140)
            openBtn:SetSize(200, 40)
            openBtn:SetText("📊 İstatistikleri Görüntüle")
            openBtn:SetFont("DermaDefaultBold")
            openBtn.DoClick = function()
                YetkiliSayac.Menu.Open()
            end
            
            local commandLabel = vgui.Create("DLabel", panel)
            commandLabel:SetPos(20, 200)
            commandLabel:SetSize(400, 40)
            commandLabel:SetText("Chat'te '" .. YetkiliSayac.Config.MenuCommand .. "' yazarak da menüyü açabilirsiniz.")
            commandLabel:SetTextColor(Color(150, 150, 150))
            commandLabel:SetAutoStretchVertical(true)
            commandLabel:SetWrap(true)
            
            return panel
        end)
    end
end)

-- Konsol komutu ile menü açma
concommand.Add("yetkili_sayac_menu", function()
    YetkiliSayac.Menu.Open()
end)

-- Bildirim sistemi
function YetkiliSayac.ShowNotification(text, type)
    type = type or NOTIFY_GENERIC
    
    if notification then
        notification.AddLegacy(text, type, 5)
        surface.PlaySound("buttons/lightswitch2.wav")
    else
        chat.AddText(Color(100, 255, 100), "[Yetkili Sayaç] ", Color(255, 255, 255), text)
    end
end

-- Yetkili mesleğe geçiş bildirimi
net.Receive("YetkiliSayac_JobStarted", function()
    local jobName = net.ReadString()
    YetkiliSayac.ShowNotification("Yetkili mesleğe geçtiniz: " .. jobName, NOTIFY_HINT)
end)

-- Yetkili meslekten çıkış bildirimi
net.Receive("YetkiliSayac_JobEnded", function()
    local jobName = net.ReadString()
    local duration = net.ReadUInt(32)
    local formattedTime = YetkiliSayac.FormatTime(duration)
    
    YetkiliSayac.ShowNotification("Yetkili meslekten çıktınız: " .. jobName .. " (Süre: " .. formattedTime .. ")", NOTIFY_UNDO)
end)

print("[Yetkili Sayaç] İstemci kodu yüklendi!")
