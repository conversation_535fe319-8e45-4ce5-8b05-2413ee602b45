-- Yet<PERSON><PERSON>mi - Discord Webhook

-- Yet<PERSON>liSayac tablosunun var olduğundan emin ol
if not YetkiliSayac then
    include("autorun/shared/sh_yetkili_sayac.lua")
end

YetkiliSayac.Webhook = YetkiliSayac.Webhook or {}

-- Discord webhook URL'ini ayarla
function YetkiliSayac.Webhook.SetURL(url)
    YetkiliSayac.Config.WebhookURL = url
    print("[Yet<PERSON><PERSON>] Discord Webhook URL ayarlandı!")
end

-- Discord'a mesaj gönder
function YetkiliSayac.Webhook.SendMessage(ply, jobName, duration)
    if not YetkiliSayac.Config.WebhookURL or YetkiliSayac.Config.WebhookURL == "" then
        print("[Yet<PERSON><PERSON>] Discord Webhook URL ayarlanmamış!")
        return
    end
    
    if not IsValid(ply) or not jobName or not duration then
        print("[Yet<PERSON><PERSON>] Webhook için geçersiz parametreler!")
        return
    end
    
    -- Minimum süre kontrolü
    if duration < YetkiliSayac.Config.MinimumSure then
        print("[Yet<PERSON><PERSON>] Süre minimum süreden az, webhook gönderilmiyor:", duration, "saniye")
        return
    end
    
    local playerName = ply:Nick()
    local steamID = ply:SteamID()
    local formattedTime = YetkiliSayac.FormatTime(duration)
    local totalTime = YetkiliSayac.Database.GetPlayerTotalTime(steamID, jobName)
    local formattedTotalTime = YetkiliSayac.FormatTime(totalTime)
    
    -- Embed oluştur
    local embed = {
        title = "🚔 Yetkili Meslek Çıkışı",
        color = 15158332, -- Kırmızı renk
        fields = {
            {
                name = "👤 Oyuncu",
                value = playerName .. " (" .. steamID .. ")",
                inline = true
            },
            {
                name = "💼 Meslek",
                value = jobName,
                inline = true
            },
            {
                name = "⏱️ Bu Oturumdaki Süre",
                value = formattedTime,
                inline = true
            },
            {
                name = "📊 Bu Meslekteki Toplam Süre",
                value = formattedTotalTime,
                inline = true
            },
            {
                name = "🕐 Çıkış Zamanı",
                value = os.date("%d/%m/%Y %H:%M:%S"),
                inline = true
            }
        },
        footer = {
            text = "Yetkili Sayaç Sistemi"
        },
        timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
    }
    
    local payload = {
        username = "Yetkili Sayaç Bot",
        avatar_url = "https://cdn.discordapp.com/emojis/🚔.png",
        embeds = {embed}
    }
    
    -- HTTP isteği gönder
    HTTP({
        url = YetkiliSayac.Config.WebhookURL,
        method = "POST",
        headers = {
            ["Content-Type"] = "application/json"
        },
        body = util.TableToJSON(payload),
        success = function(code, body, headers)
            print("[Yetkili Sayaç] Discord webhook başarıyla gönderildi! Kod:", code)
        end,
        failed = function(reason)
            print("[Yetkili Sayaç] Discord webhook gönderme hatası:", reason)
        end
    })
end

-- Webhook URL'ini konsol komutu ile ayarlama
concommand.Add("yetkili_sayac_webhook", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then
        ply:ChatPrint("Bu komutu kullanmak için SuperAdmin olmalısınız!")
        return
    end
    
    if not args[1] or args[1] == "" then
        print("[Yetkili Sayaç] Kullanım: yetkili_sayac_webhook <webhook_url>")
        return
    end
    
    YetkiliSayac.Webhook.SetURL(args[1])
    
    if IsValid(ply) then
        ply:ChatPrint("Discord Webhook URL başarıyla ayarlandı!")
    else
        print("[Yetkili Sayaç] Discord Webhook URL başarıyla ayarlandı!")
    end
end)

-- Test webhook komutu
concommand.Add("yetkili_sayac_test_webhook", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsSuperAdmin() then
        ply:ChatPrint("Bu komutu kullanmak için SuperAdmin olmalısınız!")
        return
    end
    
    if not YetkiliSayac.Config.WebhookURL or YetkiliSayac.Config.WebhookURL == "" then
        local msg = "Discord Webhook URL ayarlanmamış! Önce yetkili_sayac_webhook komutu ile URL'i ayarlayın."
        if IsValid(ply) then
            ply:ChatPrint(msg)
        else
            print("[Yetkili Sayaç] " .. msg)
        end
        return
    end
    
    -- Test mesajı gönder
    local testEmbed = {
        title = "🧪 Test Mesajı",
        description = "Yetkili Sayaç sistemi webhook testi başarılı!",
        color = 65280, -- Yeşil renk
        footer = {
            text = "Yetkili Sayaç Sistemi - Test"
        },
        timestamp = os.date("!%Y-%m-%dT%H:%M:%SZ")
    }
    
    local payload = {
        username = "Yetkili Sayaç Bot",
        embeds = {testEmbed}
    }
    
    HTTP({
        url = YetkiliSayac.Config.WebhookURL,
        method = "POST",
        headers = {
            ["Content-Type"] = "application/json"
        },
        body = util.TableToJSON(payload),
        success = function(code, body, headers)
            local msg = "Test webhook başarıyla gönderildi! Discord kanalınızı kontrol edin."
            if IsValid(ply) then
                ply:ChatPrint(msg)
            else
                print("[Yetkili Sayaç] " .. msg)
            end
        end,
        failed = function(reason)
            local msg = "Test webhook gönderme hatası: " .. reason
            if IsValid(ply) then
                ply:ChatPrint(msg)
            else
                print("[Yetkili Sayaç] " .. msg)
            end
        end
    })
end)
