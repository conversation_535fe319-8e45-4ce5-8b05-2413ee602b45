-- Yet<PERSON><PERSON> - Paylaşılan Kod
-- <PERSON>u dosya hem sunucu hem de istemci tarafında çalışır

-- İstemciye gönder
if SERVER then
    AddCSLuaFile()
end

YetkiliSayac = YetkiliSayac or {}

-- Konfigürasyon
YetkiliSayac.Config = {
    -- Yet<PERSON><PERSON>slekler (job command'ları)
    YetkiliMeslekler = {
        "police",
        "sheriff", 
        "swat",
        "mayor",
        "admin",
        "moderator"
    },
    
    -- Discord Webhook URL - Buraya kendi webhook URL'nizi yazın
    WebhookURL = "https://discord.com/api/webhooks/YOUR_WEBHOOK_ID/YOUR_WEBHOOK_TOKEN",
    
    -- Minimum süre (saniye) - Bu süreden az kalanlar Discord'a gönderilmez
    MinimumSure = 60,
    
    -- Veritabanı tablo adı
    DatabaseTable = "yetkili_sayac_data",
    
    -- <PERSON>ü açma komutu
    MenuCommand = "!yetkilisayac"
}

-- Yardımcı fonksiyonlar
function YetkiliSayac.IsYetkiliMeslek(job)
    if not job then return false end
    
    for _, yetkiliJob in pairs(YetkiliSayac.Config.YetkiliMeslekler) do
        if string.lower(job) == string.lower(yetkiliJob) then
            return true
        end
    end
    
    return false
end

function YetkiliSayac.FormatTime(seconds)
    if not seconds or seconds < 0 then return "0 saniye" end
    
    local days = math.floor(seconds / 86400)
    local hours = math.floor((seconds % 86400) / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    local secs = seconds % 60
    
    local timeStr = ""
    
    if days > 0 then
        timeStr = timeStr .. days .. " gün "
    end
    
    if hours > 0 then
        timeStr = timeStr .. hours .. " saat "
    end
    
    if minutes > 0 then
        timeStr = timeStr .. minutes .. " dakika "
    end
    
    if secs > 0 or timeStr == "" then
        timeStr = timeStr .. secs .. " saniye"
    end
    
    return string.Trim(timeStr)
end

-- Network strings
if SERVER then
    util.AddNetworkString("YetkiliSayac_OpenMenu")
    util.AddNetworkString("YetkiliSayac_RequestData")
    util.AddNetworkString("YetkiliSayac_SendData")
    util.AddNetworkString("YetkiliSayac_JobStarted")
    util.AddNetworkString("YetkiliSayac_JobEnded")
end

print("[Yetkili Sayaç] Paylaşılan kod yüklendi!")
