-- Yet<PERSON><PERSON> - Paylaşılan Kod
-- Bu dosya hem sunucu hem de istemci tarafında çalışır

-- İstemciye gönder
if SERVER then
    AddCSLuaFile()
end

YetkiliSayac = YetkiliSayac or {}

-- Konfigürasyon
YetkiliSayac.Config = {
    -- <PERSON><PERSON><PERSON> (job command'ları)
    YetkiliMeslekler = {
        "Mayor",
        "sheriff",
        "swat",
        "mayor",
        "admin",
        "moderator"
    },

    -- Meslek isimlerini güzelleştirme (opsiyonel)
    JobDisplayNames = {
        ["Mayor"] = "Belediye Başkanı",
        ["sheriff"] = "Şerif",
        ["swat"] = "SWAT",
        ["mayor"] = "Belediye Başkanı",
        ["admin"] = "Admin",
        ["moderator"] = "Moderatör"
    },
    
    -- Discord Webhook URL
    -- Discord sunucunuzda bir kanal oluşturun ve webhook URL'ini buraya yazın
    -- <PERSON>ş bırakırsanız Discord bildirimleri gönderilmez
    WebhookURL = "https://discord.com/api/webhooks/1381707674567315496/uG6cgq2QUy4eunY_8ujfnTrg1nuLyNIFnKVREMCdZ4aM1hO857hY96bbfFVoltvZv0pN",
    
    -- Minimum süre (saniye) - Bu süreden az kalanlar Discord'a gönderilmez
    MinimumSure = 60,
    
    -- Veritabanı tablo adı
    DatabaseTable = "yetkili_sayac_data",
    
    -- Menü açma komutu
    MenuCommand = "!yetkilisayac"
}

-- Yardımcı fonksiyonlar
function YetkiliSayac.IsYetkiliMeslek(job)
    if not job then return false end

    for _, yetkiliJob in pairs(YetkiliSayac.Config.YetkiliMeslekler) do
        if string.lower(job) == string.lower(yetkiliJob) then
            return true
        end
    end

    return false
end

function YetkiliSayac.GetJobDisplayName(job)
    if not job then return "Bilinmiyor" end

    -- Önce config'deki display name'lere bak
    if YetkiliSayac.Config.JobDisplayNames[job] then
        return YetkiliSayac.Config.JobDisplayNames[job]
    end

    -- Yoksa job ismini güzelleştir
    local displayName = string.gsub(job, "^%l", string.upper) -- İlk harfi büyük yap
    return displayName
end

function YetkiliSayac.FormatTime(seconds)
    -- String'i number'a çevir
    seconds = tonumber(seconds)
    if not seconds or seconds < 0 then return "0 saniye" end

    local days = math.floor(seconds / 86400)
    local hours = math.floor((seconds % 86400) / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    local secs = math.floor(seconds % 60)
    
    local timeStr = ""
    
    if days > 0 then
        timeStr = timeStr .. days .. " gün "
    end
    
    if hours > 0 then
        timeStr = timeStr .. hours .. " saat "
    end
    
    if minutes > 0 then
        timeStr = timeStr .. minutes .. " dakika "
    end
    
    if secs > 0 or timeStr == "" then
        timeStr = timeStr .. secs .. " saniye"
    end
    
    return string.Trim(timeStr)
end

-- Network strings
if SERVER then
    util.AddNetworkString("YetkiliSayac_OpenMenu")
    util.AddNetworkString("YetkiliSayac_RequestData")
    util.AddNetworkString("YetkiliSayac_SendData")
    util.AddNetworkString("YetkiliSayac_JobStarted")
    util.AddNetworkString("YetkiliSayac_JobEnded")
end

print("[Yetkili Sayaç] Paylaşılan kod yüklendi!")
