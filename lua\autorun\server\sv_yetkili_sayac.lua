-- Yet<PERSON><PERSON> - <PERSON>

-- İstemci dosyalarını gönder
AddCSLuaFile("autorun/shared/sh_yetkili_sayac.lua")
AddCSLuaFile("autorun/client/cl_yetkili_sayac.lua")
AddCSLuaFile("yetkili_sayac/cl_menu.lua")

-- Modül dosyalarını yükle
include("yetkili_sayac/sv_database.lua")
include("yetkili_sayac/sv_webhook.lua")

-- YetkiliSayac tablosunun var olduğundan emin ol
if not YetkiliSayac then
    include("autorun/shared/sh_yetkili_sayac.lua")
end

YetkiliSayac.ActiveSessions = YetkiliSayac.ActiveSessions or {}

-- Oyuncu yetkili mesleğe geçtiğinde
function YetkiliSayac.OnJobChange(ply, oldJob, newJob)
    if not IsValid(ply) then return end
    
    local steamID = ply:SteamID()
    
    -- Eski meslek yetkili ise oturumu sonlandır
    if oldJob and YetkiliSayac.IsYetkiliMeslek(oldJob) then
        local duration = YetkiliSayac.Database.EndSession(ply, oldJob)
        
        if duration and duration >= YetkiliSayac.Config.MinimumSure then
            -- Discord webhook gönder
            YetkiliSayac.Webhook.SendMessage(ply, oldJob, duration)
        end

        -- İstemciye bildirim gönder
        if duration then
            net.Start("YetkiliSayac_JobEnded")
            net.WriteString(oldJob)
            net.WriteUInt(duration, 32)
            net.Send(ply)
        end
        
        -- Aktif oturumdan kaldır
        if YetkiliSayac.ActiveSessions[steamID] then
            YetkiliSayac.ActiveSessions[steamID] = nil
        end
    end
    
    -- Yeni meslek yetkili ise oturum başlat
    if newJob and YetkiliSayac.IsYetkiliMeslek(newJob) then
        YetkiliSayac.Database.StartSession(ply, newJob)
        
        -- Aktif oturuma ekle
        YetkiliSayac.ActiveSessions[steamID] = {
            job = newJob,
            startTime = os.time(),
            player = ply
        }

        ply:ChatPrint("🚔 Yetkili mesleğe geçtiniz! Süreniz sayılmaya başladı.")

        -- İstemciye bildirim gönder
        net.Start("YetkiliSayac_JobStarted")
        net.WriteString(newJob)
        net.Send(ply)
    end
end

-- DarkRP job değişikliği hook'u
if DarkRP then
    hook.Add("OnPlayerChangedTeam", "YetkiliSayac_JobChange", function(ply, oldTeam, newTeam)
        local oldJob = oldTeam and team.GetName(oldTeam) or nil
        local newJob = newTeam and team.GetName(newTeam) or nil
        
        YetkiliSayac.OnJobChange(ply, oldJob, newJob)
    end)
else
    -- DarkRP yoksa alternatif hook
    hook.Add("PlayerChangedTeam", "YetkiliSayac_JobChange", function(ply, oldTeam, newTeam)
        local oldJob = oldTeam and team.GetName(oldTeam) or nil
        local newJob = newTeam and team.GetName(newTeam) or nil
        
        YetkiliSayac.OnJobChange(ply, oldJob, newJob)
    end)
end

-- Oyuncu sunucudan ayrıldığında
hook.Add("PlayerDisconnected", "YetkiliSayac_PlayerDisconnect", function(ply)
    if not IsValid(ply) then return end
    
    local steamID = ply:SteamID()
    
    -- Aktif oturum var mı kontrol et
    if YetkiliSayac.ActiveSessions[steamID] then
        local session = YetkiliSayac.ActiveSessions[steamID]
        local duration = YetkiliSayac.Database.EndSession(ply, session.job)
        
        if duration and duration >= YetkiliSayac.Config.MinimumSure then
            -- Discord webhook gönder
            YetkiliSayac.Webhook.SendMessage(ply, session.job, duration)
        end
        
        -- Aktif oturumdan kaldır
        YetkiliSayac.ActiveSessions[steamID] = nil
    end
end)

-- Oyuncu sunucuya katıldığında mevcut mesleğini kontrol et
hook.Add("PlayerInitialSpawn", "YetkiliSayac_PlayerSpawn", function(ply)
    timer.Simple(5, function() -- 5 saniye bekle ki oyuncu tam yüklensin
        if not IsValid(ply) then return end
        
        local currentJob = team.GetName(ply:Team())
        
        if YetkiliSayac.IsYetkiliMeslek(currentJob) then
            YetkiliSayac.Database.StartSession(ply, currentJob)
            
            local steamID = ply:SteamID()
            YetkiliSayac.ActiveSessions[steamID] = {
                job = currentJob,
                startTime = os.time(),
                player = ply
            }
            
            ply:ChatPrint("🚔 Yetkili mesleğiniz tespit edildi! Süreniz sayılmaya başladı.")
        end
    end)
end)

-- Chat komutu ile menü açma
hook.Add("PlayerSay", "YetkiliSayac_ChatCommand", function(ply, text)
    if string.lower(text) == string.lower(YetkiliSayac.Config.MenuCommand) then
        net.Start("YetkiliSayac_OpenMenu")
        net.Send(ply)
        return ""
    end
end)

-- İstemciden veri isteği geldiğinde
net.Receive("YetkiliSayac_RequestData", function(len, ply)
    if not IsValid(ply) then return end
    
    local steamID = ply:SteamID()
    
    -- Oyuncunun verilerini topla
    local data = {
        history = YetkiliSayac.Database.GetPlayerHistory(steamID, 20),
        totalTimes = {},
        currentSession = nil
    }
    
    -- Her meslek için toplam süreyi hesapla
    for _, job in pairs(YetkiliSayac.Config.YetkiliMeslekler) do
        data.totalTimes[job] = YetkiliSayac.Database.GetPlayerTotalTime(steamID, job)
    end
    
    -- Aktif oturum var mı kontrol et
    if YetkiliSayac.ActiveSessions[steamID] then
        local session = YetkiliSayac.ActiveSessions[steamID]
        data.currentSession = {
            job = session.job,
            startTime = session.startTime,
            currentTime = os.time()
        }
    end
    
    -- Veriyi istemciye gönder
    net.Start("YetkiliSayac_SendData")
    net.WriteTable(data)
    net.Send(ply)
end)

-- Konsol komutları
concommand.Add("yetkili_sayac_stats", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsAdmin() then
        ply:ChatPrint("Bu komutu kullanmak için Admin olmalısınız!")
        return
    end
    
    local stats = YetkiliSayac.Database.GetAllStats()
    
    if IsValid(ply) then
        ply:ChatPrint("=== Yetkili Sayaç İstatistikleri ===")
        for _, stat in pairs(stats) do
            ply:ChatPrint(string.format("%s - %s: %s (%d oturum)", 
                stat.player_name, stat.job_name, 
                YetkiliSayac.FormatTime(stat.total_time), 
                stat.session_count))
        end
    else
        print("=== Yetkili Sayaç İstatistikleri ===")
        for _, stat in pairs(stats) do
            print(string.format("%s - %s: %s (%d oturum)", 
                stat.player_name, stat.job_name, 
                YetkiliSayac.FormatTime(stat.total_time), 
                stat.session_count))
        end
    end
end)

print("[Yetkili Sayaç] Sunucu kodu yüklendi!")
