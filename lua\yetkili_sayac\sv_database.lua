-- Yet<PERSON><PERSON> - Veritabanı İşlemleri

-- YetkiliSayac tablosunun var olduğundan emin ol
if not YetkiliSayac then
    include("autorun/shared/sh_yetkili_sayac.lua")
end

YetkiliSayac.Database = YetkiliSayac.Database or {}

-- Veritabanı tablosunu oluştur
function YetkiliSayac.Database.CreateTable()
    local query = [[
        CREATE TABLE IF NOT EXISTS ]] .. YetkiliSayac.Config.DatabaseTable .. [[ (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            steamid VARCHAR(32) NOT NULL,
            player_name VARCHAR(64) NOT NULL,
            job_name VARCHAR(64) NOT NULL,
            start_time INTEGER NOT NULL,
            end_time INTEGER,
            duration INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ]]
    
    sql.Query(query)
    
    -- İndeks oluştur
    sql.Query("CREATE INDEX IF NOT EXISTS idx_steamid ON " .. YetkiliSayac.Config.DatabaseTable .. " (steamid)")
    sql.Query("CREATE INDEX IF NOT EXISTS idx_job_name ON " .. YetkiliSayac.Config.DatabaseTable .. " (job_name)")
    
    print("[Yetkili Sayaç] Veritabanı tablosu oluşturuldu!")
end

-- Yeni oturum başlat
function YetkiliSayac.Database.StartSession(ply, jobName)
    if not IsValid(ply) or not jobName then return false end
    
    local steamID = ply:SteamID()
    local playerName = sql.SQLStr(ply:Nick())
    local jobNameStr = sql.SQLStr(jobName)
    local currentTime = os.time()
    
    local query = string.format([[
        INSERT INTO %s (steamid, player_name, job_name, start_time)
        VALUES (%s, %s, %s, %d)
    ]], YetkiliSayac.Config.DatabaseTable, sql.SQLStr(steamID), playerName, jobNameStr, currentTime)
    
    local result = sql.Query(query)
    
    if result == false then
        print("[Yetkili Sayaç] Veritabanı hatası:", sql.LastError())
        return false
    end
    
    print("[Yetkili Sayaç] Yeni oturum başlatıldı:", ply:Nick(), "->", jobName)
    return true
end

-- Oturumu sonlandır
function YetkiliSayac.Database.EndSession(ply, jobName)
    if not IsValid(ply) or not jobName then return false end
    
    local steamID = ply:SteamID()
    local currentTime = os.time()
    
    -- Aktif oturumu bul
    local findQuery = string.format([[
        SELECT id, start_time FROM %s 
        WHERE steamid = %s AND job_name = %s AND end_time IS NULL 
        ORDER BY start_time DESC LIMIT 1
    ]], YetkiliSayac.Config.DatabaseTable, sql.SQLStr(steamID), sql.SQLStr(jobName))
    
    local result = sql.Query(findQuery)
    
    if not result or #result == 0 then
        print("[Yetkili Sayaç] Sonlandırılacak aktif oturum bulunamadı:", ply:Nick(), jobName)
        return false
    end
    
    local sessionID = result[1].id
    local startTime = tonumber(result[1].start_time)
    local duration = currentTime - startTime
    
    -- Oturumu güncelle
    local updateQuery = string.format([[
        UPDATE %s SET end_time = %d, duration = %d 
        WHERE id = %d
    ]], YetkiliSayac.Config.DatabaseTable, currentTime, duration, sessionID)
    
    local updateResult = sql.Query(updateQuery)
    
    if updateResult == false then
        print("[Yetkili Sayaç] Oturum sonlandırma hatası:", sql.LastError())
        return false
    end
    
    print("[Yetkili Sayaç] Oturum sonlandırıldı:", ply:Nick(), "->", jobName, "Süre:", YetkiliSayac.FormatTime(duration))
    return duration
end

-- Oyuncunun toplam süresini getir
function YetkiliSayac.Database.GetPlayerTotalTime(steamID, jobName)
    if not steamID then return 0 end
    
    local query
    if jobName then
        query = string.format([[
            SELECT SUM(duration) as total FROM %s 
            WHERE steamid = %s AND job_name = %s AND duration > 0
        ]], YetkiliSayac.Config.DatabaseTable, sql.SQLStr(steamID), sql.SQLStr(jobName))
    else
        query = string.format([[
            SELECT SUM(duration) as total FROM %s 
            WHERE steamid = %s AND duration > 0
        ]], YetkiliSayac.Config.DatabaseTable, sql.SQLStr(steamID))
    end
    
    local result = sql.Query(query)
    
    if result and result[1] and result[1].total then
        return tonumber(result[1].total) or 0
    end
    
    return 0
end

-- Oyuncunun geçmişini getir
function YetkiliSayac.Database.GetPlayerHistory(steamID, limit)
    if not steamID then return {} end
    
    limit = limit or 10
    
    local query = string.format([[
        SELECT player_name, job_name, start_time, end_time, duration
        FROM %s
        WHERE steamid = %s AND duration > 0
        ORDER BY start_time DESC
        LIMIT %d
    ]], YetkiliSayac.Config.DatabaseTable, sql.SQLStr(steamID), limit)
    
    local result = sql.Query(query)
    
    return result or {}
end

-- Tüm oyuncuların istatistiklerini getir
function YetkiliSayac.Database.GetAllStats()
    local query = string.format([[
        SELECT steamid, player_name, job_name, SUM(duration) as total_time, COUNT(*) as session_count
        FROM %s 
        WHERE duration > 0 
        GROUP BY steamid, job_name 
        ORDER BY total_time DESC
    ]], YetkiliSayac.Config.DatabaseTable)
    
    local result = sql.Query(query)
    
    return result or {}
end

-- Veritabanını başlat
hook.Add("Initialize", "YetkiliSayac_InitDatabase", function()
    YetkiliSayac.Database.CreateTable()
end)
